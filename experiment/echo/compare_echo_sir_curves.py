#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Echo室SIR曲线对比程序

配置参数：
- 请修改下面的 DIR1 和 DIR2 变量来指定要对比的两个目录
- 可以自定义标签名称
- 增加了知识分子和普通群众的SIR曲线对比图
"""

# ==================== 配置参数 ====================
DIR1 = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250817_205417"  # 第一个目录路径（有SIR数据）
DIR2 = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250820_192149"  # 第二个目录路径（有SIR数据）
LABEL1 = "Community A"  # 第一个数据集的标签
LABEL2 = "Community B"  # 第二个数据集的标签

# 模拟exponential模型参数（用于演示）
MOCK_EXP_PARAMS1 = {
    'lambda_0': 0.334274,
    'lambda_1': -0.100000,
    'mu_0': 0.021563,
    'mu_1': -0.008605
}

MOCK_EXP_PARAMS2 = {
    'lambda_0': 0.328571,
    'lambda_1': -0.088009,
    'mu_0': 0.031248,
    'mu_1': -0.008317
}
# ==================================================

import os
import sys
import json
import glob
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import logging
from matplotlib.backends.backend_pdf import PdfPages
from matplotlib.lines import Line2D
import re

# 设置字体 - 确保使用Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_sir_data(log_dir: str):
    """
    从日志目录加载所有SIR数据

    Args:
        log_dir: 日志目录路径

    Returns:
        包含所有SIR数据的字典
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    # 首先尝试加载sir_metrics.json格式的文件
    sir_files = glob.glob(os.path.join(log_dir, "*sir_metrics.json"))
    sir_data = {}

    if sir_files:
        # 使用原有的sir_metrics.json格式
        for file_path in sir_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 提取轮次和阶段信息
                round_num = data.get('round', 0)
                stage = data.get('stage', '')
                key = f"round_{round_num}_{stage}"

                sir_data[key] = data

            except Exception as e:
                logger.warning(f"读取文件 {file_path} 时出错: {e}")
                continue

        logger.info(f"从 {log_dir} 成功加载 {len(sir_data)} 个SIR数据文件")
        return sir_data

    # 如果没有sir_metrics.json文件，尝试加载sir_curves_data.json格式
    curves_file = os.path.join(log_dir, "sir_curves_data.json")
    if os.path.exists(curves_file):
        return load_sir_curves_data(curves_file)

    logger.error(f"未找到SIR数据文件在目录: {log_dir}")
    return {}

def load_sir_curves_data(curves_file: str):
    """
    从sir_curves_data.json文件加载SIR数据

    Args:
        curves_file: sir_curves_data.json文件路径

    Returns:
        转换为sir_metrics格式的数据字典
    """
    try:
        with open(curves_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        sir_data = {}
        rounds = data.get('rounds', [])
        total_pop = data.get('total_population', {})

        # 转换数据格式为sir_metrics兼容格式
        for i, round_num in enumerate(rounds):
            # 创建after阶段的数据
            sir_data[f"round_{round_num}_after"] = {
                'round': round_num,
                'stage': 'after',
                'susceptible': {
                    'ratio': total_pop.get('susceptible', [])[i] if i < len(total_pop.get('susceptible', [])) else 0
                },
                'infected': {
                    'ratio': total_pop.get('infected', [])[i] if i < len(total_pop.get('infected', [])) else 0
                },
                'recovered': {
                    'ratio': total_pop.get('recovered', [])[i] if i < len(total_pop.get('recovered', [])) else 0
                },
                'group_analysis': {
                    'intellectual': {
                        'susceptible': {'ratio': data.get('intellectual_group', {}).get('susceptible', [])[i] if i < len(data.get('intellectual_group', {}).get('susceptible', [])) else 0},
                        'infected': {'ratio': data.get('intellectual_group', {}).get('infected', [])[i] if i < len(data.get('intellectual_group', {}).get('infected', [])) else 0},
                        'recovered': {'ratio': data.get('intellectual_group', {}).get('recovered', [])[i] if i < len(data.get('intellectual_group', {}).get('recovered', [])) else 0}
                    },
                    'regular': {
                        'susceptible': {'ratio': data.get('regular_group', {}).get('susceptible', [])[i] if i < len(data.get('regular_group', {}).get('susceptible', [])) else 0},
                        'infected': {'ratio': data.get('regular_group', {}).get('infected', [])[i] if i < len(data.get('regular_group', {}).get('infected', [])) else 0},
                        'recovered': {'ratio': data.get('regular_group', {}).get('recovered', [])[i] if i < len(data.get('regular_group', {}).get('recovered', [])) else 0}
                    }
                }
            }

        logger.info(f"从 {curves_file} 成功加载并转换 {len(sir_data)} 个SIR数据点")
        return sir_data

    except Exception as e:
        logger.error(f"读取sir_curves_data.json文件时出错 {curves_file}: {e}")
        return {}

def extract_sir_trends(sir_data: dict):
    """
    从SIR数据中提取趋势数据
    
    Args:
        sir_data: SIR数据字典
        
    Returns:
        包含总体和各群体趋势数据的字典
    """
    # 获取所有轮次
    rounds = set()
    for key in sir_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)
    
    rounds = sorted(list(rounds))
    logger.info(f"发现 {len(rounds)} 个轮次的数据: {rounds}")

    if not rounds:
        logger.error("未找到任何轮次数据")
        return None

    # 初始化趋势数据
    trends = {
        'rounds': rounds,
        'total': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        },
        'intellectual': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        },
        'regular': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        }
    }
    
    # 提取每轮的数据（优先使用after数据，如果没有则使用before数据）
    for round_num in rounds:
        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"
        
        # 选择数据源
        if after_key in sir_data:
            data = sir_data[after_key]
        elif before_key in sir_data:
            data = sir_data[before_key]
        else:
            logger.warning(f"轮次 {round_num} 没有找到SIR数据")
            # 填充空数据
            trends['total']['susceptible'].append(0)
            trends['total']['infected'].append(0)
            trends['total']['recovered'].append(0)
            trends['intellectual']['susceptible'].append(0)
            trends['intellectual']['infected'].append(0)
            trends['intellectual']['recovered'].append(0)
            trends['regular']['susceptible'].append(0)
            trends['regular']['infected'].append(0)
            trends['regular']['recovered'].append(0)
            continue
        
        # 提取总体数据
        trends['total']['susceptible'].append(data.get('susceptible', {}).get('ratio', 0))
        trends['total']['infected'].append(data.get('infected', {}).get('ratio', 0))
        trends['total']['recovered'].append(data.get('recovered', {}).get('ratio', 0))
        
        # 提取群体数据
        group_analysis = data.get('group_analysis', {})
        
        # 知识分子群体
        intellectual_data = group_analysis.get('intellectual', {})
        trends['intellectual']['susceptible'].append(intellectual_data.get('susceptible', {}).get('ratio', 0))
        trends['intellectual']['infected'].append(intellectual_data.get('infected', {}).get('ratio', 0))
        trends['intellectual']['recovered'].append(intellectual_data.get('recovered', {}).get('ratio', 0))
        
        # 普通群众群体
        regular_data = group_analysis.get('regular', {})
        trends['regular']['susceptible'].append(regular_data.get('susceptible', {}).get('ratio', 0))
        trends['regular']['infected'].append(regular_data.get('infected', {}).get('ratio', 0))
        trends['regular']['recovered'].append(regular_data.get('recovered', {}).get('ratio', 0))
    
    return trends

def load_best_model_params(json_file_path: str):
    """
    从sir_model_comparison.json文件中加载best_model的参数

    Args:
        json_file_path: JSON文件路径

    Returns:
        包含best_model参数的字典，如果解析失败返回None
    """
    if not os.path.exists(json_file_path):
        logger.warning(f"JSON文件不存在: {json_file_path}")
        return None

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 获取best_model信息
        comparison_results = data.get('comparison_results', {}).get('total', {})
        best_model = comparison_results.get('best_model', '')

        if best_model and best_model in comparison_results:
            model_data = comparison_results[best_model]

            # 如果是exponential模型，提取参数
            if best_model == 'exponential' and 'parameters' in model_data:
                params = model_data['parameters']
                if len(params) >= 4:
                    return {
                        'model_type': 'exponential',
                        'lambda_0': params[0],
                        'lambda_1': params[1],
                        'mu_0': params[2],
                        'mu_1': params[3]
                    }

            # 如果是其他模型类型，也可以在这里扩展
            logger.warning(f"不支持的best_model类型: {best_model}")
            return None
        else:
            logger.warning(f"未找到best_model信息: {json_file_path}")
            return None

    except Exception as e:
        logger.error(f"解析JSON文件时出错 {json_file_path}: {e}")
        return None

def parse_exponential_model_params(report_file_path: str):
    """
    从sir_model_comparison_report.txt文件中解析exponential模型的参数

    Args:
        report_file_path: 报告文件路径

    Returns:
        包含exponential模型参数的字典，如果解析失败返回None
    """
    if not os.path.exists(report_file_path):
        logger.warning(f"报告文件不存在: {report_file_path}")
        return None

    try:
        with open(report_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找exponential模型的参数
        # 传播率: λ(t) = 0.334274·exp(-0.100000·t)
        # 恢复率: μ(t) = 0.021563·exp(-0.008605·t)

        lambda_match = re.search(r'传播率: λ\(t\) = ([\d.]+)·exp\(([-\d.]+)·t\)', content)
        mu_match = re.search(r'恢复率: μ\(t\) = ([\d.]+)·exp\(([-\d.]+)·t\)', content)

        if lambda_match and mu_match:
            lambda_0 = float(lambda_match.group(1))
            lambda_1 = float(lambda_match.group(2))
            mu_0 = float(mu_match.group(1))
            mu_1 = float(mu_match.group(2))

            return {
                'model_type': 'exponential',
                'lambda_0': lambda_0,
                'lambda_1': lambda_1,
                'mu_0': mu_0,
                'mu_1': mu_1
            }
        else:
            logger.warning(f"无法从报告文件中解析exponential模型参数: {report_file_path}")
            return None

    except Exception as e:
        logger.error(f"解析报告文件时出错 {report_file_path}: {e}")
        return None

def plot_comparison_sir_curves(trends1: dict, trends2: dict, label1: str, label2: str,
                              dir1: str, dir2: str,
                              output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/echo"):
    """
    绘制两个目录的SIR对比曲线图，包含三个子图：
    1. 总体SIR曲线对比
    2. Best Model函数对比
    3. 知识分子和普通群众的SIR曲线对比

    Args:
        trends1: 第一个目录的趋势数据
        trends2: 第二个目录的趋势数据
        label1: 第一个目录的标签
        label2: 第二个目录的标签
        dir1: 第一个目录路径
        dir2: 第二个目录路径
        output_dir: 输出目录
    """
    # 创建比较结果目录
    comparison_dir = os.path.join(output_dir, f"comparison_{label1}_vs_{label2}")
    os.makedirs(comparison_dir, exist_ok=True)

    # 加载best_model参数
    json1_path = os.path.join(dir1, "sir_model_comparison.json")
    json2_path = os.path.join(dir2, "sir_model_comparison.json")

    exp_params1 = load_best_model_params(json1_path)
    exp_params2 = load_best_model_params(json2_path)

    # 如果无法从JSON文件解析参数，尝试从报告文件解析
    if not exp_params1:
        report1_path = os.path.join(dir1, "sir_model_comparison_report.txt")
        exp_params1 = parse_exponential_model_params(report1_path)

    if not exp_params2:
        report2_path = os.path.join(dir2, "sir_model_comparison_report.txt")
        exp_params2 = parse_exponential_model_params(report2_path)

    # 如果仍然无法解析参数，使用模拟参数
    if not exp_params1:
        exp_params1 = MOCK_EXP_PARAMS1.copy()
        exp_params1['model_type'] = 'exponential'
        logger.info(f"使用模拟exponential参数 for {label1}")

    if not exp_params2:
        exp_params2 = MOCK_EXP_PARAMS2.copy()
        exp_params2['model_type'] = 'exponential'
        logger.info(f"使用模拟exponential参数 for {label2}")

    # 设置图形样式
    plt.style.use('default')

    # 左图和右图都使用莫兰迪色系，但选择不同的颜色以增强对比
    # 左图：经典莫兰迪色系（SIR曲线）
    colors_left = {
        'susceptible': '#8FBC8F',  # 莫兰迪绿色
        'infected': '#CD5C5C',     # 莫兰迪红色
        'recovered': '#6495ED'     # 莫兰迪蓝色
    }

    # 右图：对比度更强的莫兰迪色系（模型函数）
    colors_right = {
        'lambda': '#B8860B',       # 莫兰迪金色 - 传播率
        'mu': '#8B4A8B'            # 莫兰迪紫色 - 恢复率
    }

    # 第三图：群体对比色系
    colors_groups = {
        'intellectual': '#9370DB',  # 紫色 - 知识分子
        'regular': '#20B2AA'        # 青色 - 普通群众
    }

    # 使用较短的轮次范围（取两者的交集）
    rounds1 = trends1['rounds']
    rounds2 = trends2['rounds']

    # 找到共同的轮次范围
    min_round = max(min(rounds1), min(rounds2))
    max_round = min(max(rounds1), max(rounds2))
    common_rounds = list(range(min_round, max_round + 1))

    # 提取对应轮次的数据
    def extract_common_data(trends, common_rounds):
        """提取共同轮次的数据"""
        original_rounds = trends['rounds']
        common_data = {
            'rounds': common_rounds,
            'total': {'susceptible': [], 'infected': [], 'recovered': []},
            'intellectual': {'susceptible': [], 'infected': [], 'recovered': []},
            'regular': {'susceptible': [], 'infected': [], 'recovered': []}
        }

        for round_num in common_rounds:
            if round_num in original_rounds:
                idx = original_rounds.index(round_num)
                for group in ['total', 'intellectual', 'regular']:
                    for state in ['susceptible', 'infected', 'recovered']:
                        common_data[group][state].append(trends[group][state][idx])
            else:
                # 如果某轮次数据缺失，填充0
                for group in ['total', 'intellectual', 'regular']:
                    for state in ['susceptible', 'infected', 'recovered']:
                        common_data[group][state].append(0)

        return common_data

    trends1_common = extract_common_data(trends1, common_rounds)
    trends2_common = extract_common_data(trends2, common_rounds)

    logger.info(f"使用共同轮次范围: {min_round}-{max_round}, 共{len(common_rounds)}轮")

    # 创建三个子图，为底部图例留出空间
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 5))

    # 创建进度标签（使用百分数格式）
    total_rounds = len(common_rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    # 设置x轴刻度和标签 - 保留5-8个刻度
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [common_rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    # 第一个子图：总体SIR曲线对比
    # 第一个目录（实线）
    ax1.plot(common_rounds, trends1_common['total']['susceptible'], 'o-', color=colors_left['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label1} - Susceptible')
    ax1.plot(common_rounds, trends1_common['total']['infected'], 's-', color=colors_left['infected'],
             linewidth=2, markersize=1.5, label=f'{label1} - Infected')
    ax1.plot(common_rounds, trends1_common['total']['recovered'], '^-', color=colors_left['recovered'],
             linewidth=2, markersize=1.5, label=f'{label1} - Recovered')

    # 第二个目录（虚线）
    ax1.plot(common_rounds, trends2_common['total']['susceptible'], 'o--', color=colors_left['susceptible'],
             linewidth=2, markersize=1.5, label=f'{label2} - Susceptible')
    ax1.plot(common_rounds, trends2_common['total']['infected'], 's--', color=colors_left['infected'],
             linewidth=2, markersize=1.5, label=f'{label2} - Infected')
    ax1.plot(common_rounds, trends2_common['total']['recovered'], '^--', color=colors_left['recovered'],
             linewidth=2, markersize=1.5, label=f'{label2} - Recovered')

    ax1.set_xlabel('Period', fontsize=10, fontfamily='Times New Roman')
    ax1.set_ylabel('Proportion', fontsize=10, fontfamily='Times New Roman')
    ax1.set_title('Total Population SIR Curves', fontsize=11, fontfamily='Times New Roman')
    ax1.set_xticks(selected_rounds)
    ax1.set_xticklabels(selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')
    ax1.tick_params(axis='y', labelsize=10)
    # 设置y轴刻度标签字体
    for label in ax1.get_yticklabels():
        label.set_fontfamily('Times New Roman')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)

    # 第二个子图：Best Model函数对比
    if exp_params1 and exp_params2:
        # 创建时间序列用于绘制函数曲线，使用与左图相同的轮次范围
        t_range = np.linspace(min_round, max_round, 100)

        # 创建百分比标签用于右图横坐标，与左图保持一致的分布
        t_range_percent = [(t - min_round) / (max_round - min_round) * 100 for t in t_range]

        # 计算第一个模型的函数值
        lambda1_t = exp_params1['lambda_0'] * np.exp(exp_params1['lambda_1'] * t_range)
        mu1_t = exp_params1['mu_0'] * np.exp(exp_params1['mu_1'] * t_range)

        # 计算第二个模型的函数值
        lambda2_t = exp_params2['lambda_0'] * np.exp(exp_params2['lambda_1'] * t_range)
        mu2_t = exp_params2['mu_0'] * np.exp(exp_params2['mu_1'] * t_range)

        # 绘制传播率函数
        ax2.plot(t_range_percent, lambda1_t, '-', color=colors_right['lambda'], linewidth=2,
                label=f'{label1} - λ(t)')
        ax2.plot(t_range_percent, lambda2_t, '--', color=colors_right['lambda'], linewidth=2,
                label=f'{label2} - λ(t)')

        # 绘制恢复率函数
        ax2.plot(t_range_percent, mu1_t, '-', color=colors_right['mu'], linewidth=2,
                label=f'{label1} - μ(t)')
        ax2.plot(t_range_percent, mu2_t, '--', color=colors_right['mu'], linewidth=2,
                label=f'{label2} - μ(t)')

        ax2.set_xlabel('Period', fontsize=10, fontfamily='Times New Roman')
        ax2.set_ylabel('Rate', fontsize=10, fontfamily='Times New Roman')
        ax2.set_title('Best Model Functions', fontsize=11, fontfamily='Times New Roman')

        # 设置x轴刻度和标签，与左图保持一致的分布
        # 使用与左图相同的刻度选择逻辑
        selected_percent = [(common_rounds[i] - min_round) / (max_round - min_round) * 100 for i in tick_indices]
        ax2.set_xticks(selected_percent)
        ax2.set_xticklabels(selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')

        ax2.tick_params(axis='y', labelsize=10)
        # 设置ax2的刻度标签字体
        for label in ax2.get_yticklabels():
            label.set_fontfamily('Times New Roman')
        ax2.grid(True, alpha=0.3)
    else:
        ax2.text(0.5, 0.5, 'Best model\nparameters not available',
                ha='center', va='center', transform=ax2.transAxes, fontsize=10)
        ax2.set_title('Best Model Functions', fontsize=11, fontfamily='Times New Roman')

    # 第三个子图：知识分子和普通群众的SIR曲线对比（使用第二个目录的数据）
    # 知识分子群体（实线）
    ax3.plot(common_rounds, trends2_common['intellectual']['susceptible'], 'o-', color=colors_groups['intellectual'],
             linewidth=2, markersize=1.5, label='Intellectual - Susceptible')
    ax3.plot(common_rounds, trends2_common['intellectual']['infected'], 's-', color=colors_groups['intellectual'],
             linewidth=2, markersize=1.5, label='Intellectual - Infected', alpha=0.7)
    ax3.plot(common_rounds, trends2_common['intellectual']['recovered'], '^-', color=colors_groups['intellectual'],
             linewidth=2, markersize=1.5, label='Intellectual - Recovered', alpha=0.5)

    # 普通群众群体（虚线）
    ax3.plot(common_rounds, trends2_common['regular']['susceptible'], 'o--', color=colors_groups['regular'],
             linewidth=2, markersize=1.5, label='Regular - Susceptible')
    ax3.plot(common_rounds, trends2_common['regular']['infected'], 's--', color=colors_groups['regular'],
             linewidth=2, markersize=1.5, label='Regular - Infected', alpha=0.7)
    ax3.plot(common_rounds, trends2_common['regular']['recovered'], '^--', color=colors_groups['regular'],
             linewidth=2, markersize=1.5, label='Regular - Recovered', alpha=0.5)

    ax3.set_xlabel('Period', fontsize=10, fontfamily='Times New Roman')
    ax3.set_ylabel('Proportion', fontsize=10, fontfamily='Times New Roman')
    ax3.set_title(f'{label2} - Group Comparison', fontsize=11, fontfamily='Times New Roman')
    ax3.set_xticks(selected_rounds)
    ax3.set_xticklabels(selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')
    ax3.tick_params(axis='y', labelsize=10)
    # 设置y轴刻度标签字体
    for label in ax3.get_yticklabels():
        label.set_fontfamily('Times New Roman')
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, 1)

    # 创建简洁的图例：只显示实线和虚线代表的数据集，以及颜色含义
    # 创建自定义图例元素
    legend_elements = [
        # 数据集说明
        Line2D([0], [0], color='black', linestyle='-', linewidth=2, label=f'{label1} (Solid)'),
        Line2D([0], [0], color='black', linestyle='--', linewidth=2, label=f'{label2} (Dashed)'),
        # 颜色含义说明
        Line2D([0], [0], color=colors_left['susceptible'], linestyle='-', linewidth=2, label='S'),
        Line2D([0], [0], color=colors_left['infected'], linestyle='-', linewidth=2, label='I'),
        Line2D([0], [0], color=colors_left['recovered'], linestyle='-', linewidth=2, label='R'),
    ]

    # 如果有模型参数，添加右图的颜色说明
    if exp_params1 and exp_params2:
        legend_elements.extend([
            Line2D([0], [0], color=colors_right['lambda'], linestyle='-', linewidth=2, label='λ(t)'),
            Line2D([0], [0], color=colors_right['mu'], linestyle='-', linewidth=2, label='μ(t)'),
        ])

    # 添加群体对比的颜色说明
    legend_elements.extend([
        Line2D([0], [0], color=colors_groups['intellectual'], linestyle='-', linewidth=2, label='Intellectual'),
        Line2D([0], [0], color=colors_groups['regular'], linestyle='--', linewidth=2, label='Regular'),
    ])

    # 在图的底部创建统一的图例，横向排列，无边框
    fig.legend(handles=legend_elements, loc='lower center', bbox_to_anchor=(0.5, -0.05),
               ncol=len(legend_elements), fontsize=9, frameon=False, prop={'family': 'Times New Roman'})

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)  # 为底部图例留出空间

    # 保存PNG格式
    total_output_path_png = os.path.join(comparison_dir, 'echo_sir_comparison.png')
    plt.savefig(total_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    total_output_path_pdf = os.path.join(comparison_dir, 'echo_sir_comparison.pdf')
    plt.savefig(total_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"Echo室SIR曲线对比图已保存到: {total_output_path_png}")
    logger.info(f"Echo室SIR曲线对比图PDF已保存到: {total_output_path_pdf}")

    # 2. 保存对比数据JSON
    save_comparison_data_json(trends1_common, trends2_common, label1, label2, comparison_dir, exp_params1, exp_params2)

    # 3. 生成对比分析报告
    generate_comparison_report(trends1_common, trends2_common, label1, label2, comparison_dir, exp_params1, exp_params2)

def save_comparison_data_json(trends1: dict, trends2: dict, label1: str, label2: str, output_dir: str,
                             exp_params1: dict = None, exp_params2: dict = None):
    """
    保存对比数据为JSON文件

    Args:
        trends1: 第一个目录的趋势数据
        trends2: 第二个目录的趋势数据
        label1: 第一个目录的标签
        label2: 第二个目录的标签
        output_dir: 输出目录
        exp_params1: 第一个目录的exponential模型参数
        exp_params2: 第二个目录的exponential模型参数
    """
    # 构建输出数据
    output_data = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "comparison_type": "Echo室SIR curves and exponential model comparison with group analysis",
            "dataset1": label1,
            "dataset2": label2,
            "total_rounds": len(trends1['rounds']),
            "rounds_range": f"{min(trends1['rounds'])}-{max(trends1['rounds'])}",
            "description": "Echo室SIR model comparison data for virtual community experiment (total population and group analysis)"
        },
        "rounds": trends1['rounds'],
        "dataset1": {
            "label": label1,
            "total_population": {
                "susceptible": trends1['total']['susceptible'],
                "infected": trends1['total']['infected'],
                "recovered": trends1['total']['recovered']
            },
            "intellectual_group": {
                "susceptible": trends1['intellectual']['susceptible'],
                "infected": trends1['intellectual']['infected'],
                "recovered": trends1['intellectual']['recovered']
            },
            "regular_group": {
                "susceptible": trends1['regular']['susceptible'],
                "infected": trends1['regular']['infected'],
                "recovered": trends1['regular']['recovered']
            },
            "exponential_model": exp_params1 if exp_params1 else None
        },
        "dataset2": {
            "label": label2,
            "total_population": {
                "susceptible": trends2['total']['susceptible'],
                "infected": trends2['total']['infected'],
                "recovered": trends2['total']['recovered']
            },
            "intellectual_group": {
                "susceptible": trends2['intellectual']['susceptible'],
                "infected": trends2['intellectual']['infected'],
                "recovered": trends2['intellectual']['recovered']
            },
            "regular_group": {
                "susceptible": trends2['regular']['susceptible'],
                "infected": trends2['regular']['infected'],
                "recovered": trends2['regular']['recovered']
            },
            "exponential_model": exp_params2 if exp_params2 else None
        }
    }

    # 保存JSON文件
    json_output_path = os.path.join(output_dir, 'echo_sir_comparison_data.json')
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"Echo室SIR对比数据已保存到: {json_output_path}")

def generate_comparison_report(trends1: dict, trends2: dict, label1: str, label2: str, output_dir: str,
                             exp_params1: dict = None, exp_params2: dict = None):
    """
    生成对比分析报告

    Args:
        trends1: 第一个目录的趋势数据
        trends2: 第二个目录的趋势数据
        label1: 第一个目录的标签
        label2: 第二个目录的标签
        output_dir: 输出目录
        exp_params1: 第一个目录的exponential模型参数
        exp_params2: 第二个目录的exponential模型参数
    """
    rounds = trends1['rounds']
    total_rounds = len(rounds)

    # 计算关键指标
    def calculate_metrics(data):
        """计算各种指标"""
        susceptible = data['susceptible']
        infected = data['infected']
        recovered = data['recovered']

        # 初始和最终状态
        initial_s, initial_i, initial_r = susceptible[0], infected[0], recovered[0]
        final_s, final_i, final_r = susceptible[-1], infected[-1], recovered[-1]

        # 感染峰值
        max_infected = max(infected)
        max_infected_round = infected.index(max_infected) + 1

        # 恢复率变化
        recovery_change = final_r - initial_r

        # 易感人群减少率
        susceptible_reduction = initial_s - final_s

        return {
            'initial': {'s': initial_s, 'i': initial_i, 'r': initial_r},
            'final': {'s': final_s, 'i': final_i, 'r': final_r},
            'max_infected': max_infected,
            'max_infected_round': max_infected_round,
            'recovery_change': recovery_change,
            'susceptible_reduction': susceptible_reduction
        }

    # 计算两个数据集的指标（总体和各群体）
    metrics1 = {
        'total': calculate_metrics(trends1['total']),
        'intellectual': calculate_metrics(trends1['intellectual']),
        'regular': calculate_metrics(trends1['regular'])
    }

    metrics2 = {
        'total': calculate_metrics(trends2['total']),
        'intellectual': calculate_metrics(trends2['intellectual']),
        'regular': calculate_metrics(trends2['regular'])
    }

    # 生成exponential模型参数对比部分
    exp_model_section = ""
    if exp_params1 and exp_params2:
        exp_model_section = f"""
==================================================
二、Exponential模型参数对比
==================================================

1. 传播率函数参数：
   - {label1}: λ(t) = {exp_params1['lambda_0']:.6f}·exp({exp_params1['lambda_1']:.6f}·t)
   - {label2}: λ(t) = {exp_params2['lambda_0']:.6f}·exp({exp_params2['lambda_1']:.6f}·t)

   参数差异：
   - λ₀差异：{abs(exp_params1['lambda_0'] - exp_params2['lambda_0']):.6f}
   - λ₁差异：{abs(exp_params1['lambda_1'] - exp_params2['lambda_1']):.6f}

2. 恢复率函数参数：
   - {label1}: μ(t) = {exp_params1['mu_0']:.6f}·exp({exp_params1['mu_1']:.6f}·t)
   - {label2}: μ(t) = {exp_params2['mu_0']:.6f}·exp({exp_params2['mu_1']:.6f}·t)

   参数差异：
   - μ₀差异：{abs(exp_params1['mu_0'] - exp_params2['mu_0']):.6f}
   - μ₁差异：{abs(exp_params1['mu_1'] - exp_params2['mu_1']):.6f}
"""
    elif exp_params1 or exp_params2:
        exp_model_section = f"""
==================================================
二、Exponential模型参数对比
==================================================

注意：只有一个数据集包含exponential模型参数，无法进行完整对比。
"""
    else:
        exp_model_section = f"""
==================================================
二、Exponential模型参数对比
==================================================

注意：两个数据集都没有找到exponential模型参数。
"""

    # 生成群体对比分析部分
    group_comparison_section = f"""
==================================================
三、群体对比分析（基于{label2}数据）
==================================================

1. 知识分子群体 vs 普通群众群体：

   感染峰值对比：
   - 知识分子：{metrics2['intellectual']['max_infected']:.1%}（第{metrics2['intellectual']['max_infected_round']}轮）
   - 普通群众：{metrics2['regular']['max_infected']:.1%}（第{metrics2['regular']['max_infected_round']}轮）
   - 差异：{abs(metrics2['intellectual']['max_infected'] - metrics2['regular']['max_infected']):.1%}

   最终恢复率对比：
   - 知识分子：{metrics2['intellectual']['final']['r']:.1%}
   - 普通群众：{metrics2['regular']['final']['r']:.1%}
   - 差异：{abs(metrics2['intellectual']['final']['r'] - metrics2['regular']['final']['r']):.1%}

   易感人群减少对比：
   - 知识分子：{metrics2['intellectual']['susceptible_reduction']:.1%}
   - 普通群众：{metrics2['regular']['susceptible_reduction']:.1%}
   - 差异：{abs(metrics2['intellectual']['susceptible_reduction'] - metrics2['regular']['susceptible_reduction']):.1%}

2. 群体特征分析：
   - {'知识分子群体更容易感染' if metrics2['intellectual']['max_infected'] > metrics2['regular']['max_infected'] else '普通群众群体更容易感染' if metrics2['regular']['max_infected'] > metrics2['intellectual']['max_infected'] else '两群体感染程度相近'}
   - {'知识分子群体恢复能力更强' if metrics2['intellectual']['final']['r'] > metrics2['regular']['final']['r'] else '普通群众群体恢复能力更强' if metrics2['regular']['final']['r'] > metrics2['intellectual']['final']['r'] else '两群体恢复能力相近'}
"""

    # 生成报告内容
    report_content = f"""Echo室虚拟社区SIR模型对比分析报告

生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
对比数据集：{label1} vs {label2}
分析轮次：第{rounds[0]}轮至第{rounds[-1]}轮，共{total_rounds}轮

==================================================
一、总体传播情况对比
==================================================

1. 感染峰值对比：
   - {label1}：{metrics1['total']['max_infected']:.1%}（第{metrics1['total']['max_infected_round']}轮）
   - {label2}：{metrics2['total']['max_infected']:.1%}（第{metrics2['total']['max_infected_round']}轮）
   - 差异：{abs(metrics1['total']['max_infected'] - metrics2['total']['max_infected']):.1%}

2. 最终恢复率对比：
   - {label1}：{metrics1['total']['final']['r']:.1%}
   - {label2}：{metrics2['total']['final']['r']:.1%}
   - 差异：{abs(metrics1['total']['final']['r'] - metrics2['total']['final']['r']):.1%}

3. 易感人群减少对比：
   - {label1}：{metrics1['total']['susceptible_reduction']:.1%}
   - {label2}：{metrics2['total']['susceptible_reduction']:.1%}
   - 差异：{abs(metrics1['total']['susceptible_reduction'] - metrics2['total']['susceptible_reduction']):.1%}
{exp_model_section}{group_comparison_section}
==================================================
四、Echo室对比总结
==================================================

基于{total_rounds}轮的对比分析，Echo室实验的主要发现：

1. 数据集对比：{'数据集1传播更强' if metrics1['total']['max_infected'] > metrics2['total']['max_infected'] else '数据集2传播更强' if metrics2['total']['max_infected'] > metrics1['total']['max_infected'] else '传播强度相近'}

2. 群体差异：{'知识分子群体在信息传播中表现出不同的模式' if abs(metrics2['intellectual']['max_infected'] - metrics2['regular']['max_infected']) > 0.05 else '知识分子和普通群众在信息传播中表现相似'}

3. 恢复能力：{'数据集1恢复能力更强' if metrics1['total']['final']['r'] > metrics2['total']['final']['r'] else '数据集2恢复能力更强' if metrics2['total']['final']['r'] > metrics1['total']['final']['r'] else '恢复能力相近'}

4. 模型特征：{'两个数据集都使用exponential变系数模型，体现了传播动力学的时变特征' if exp_params1 and exp_params2 else '模型参数信息不完整，无法进行深入的模型特征对比'}

5. Echo室效应：本分析特别关注了不同群体在信息传播中的差异化表现，为理解虚拟社区中的回音室效应提供了数据支持。

==================================================
报告结束
==================================================
"""

    # 保存报告
    report_path = os.path.join(output_dir, 'Echo室SIR对比分析报告.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    logger.info(f"Echo室对比分析报告已保存到: {report_path}")

def main():
    """
    主函数
    """
    logger.info("开始Echo室SIR曲线对比分析...")

    # 1. 加载两个目录的SIR数据
    sir_data1 = load_sir_data(DIR1)
    if not sir_data1:
        logger.error(f"未找到第一个目录的SIR数据: {DIR1}")
        return 1

    sir_data2 = load_sir_data(DIR2)
    if not sir_data2:
        logger.error(f"未找到第二个目录的SIR数据: {DIR2}")
        return 1

    # 2. 提取趋势数据
    trends1 = extract_sir_trends(sir_data1)
    if not trends1:
        logger.error("提取第一个目录的趋势数据失败")
        return 1

    trends2 = extract_sir_trends(sir_data2)
    if not trends2:
        logger.error("提取第二个目录的趋势数据失败")
        return 1

    # 3. 绘制对比曲线图
    plot_comparison_sir_curves(trends1, trends2, LABEL1, LABEL2, DIR1, DIR2)

    logger.info("Echo室SIR曲线对比分析完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
