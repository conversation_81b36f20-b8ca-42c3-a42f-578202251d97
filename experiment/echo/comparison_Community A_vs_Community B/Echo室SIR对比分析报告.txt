Echo室虚拟社区SIR模型对比分析报告

生成时间：2025年08月24日 14:40:55
对比数据集：Community A vs Community B
分析轮次：第1轮至第40轮，共40轮

==================================================
一、总体传播情况对比
==================================================

1. 感染峰值对比：
   - Community A：33.0%（第26轮）
   - Community B：25.0%（第30轮）
   - 差异：8.0%

2. 最终恢复率对比：
   - Community A：21.0%
   - Community B：30.0%
   - 差异：9.0%

3. 易感人群减少对比：
   - Community A：43.0%
   - Community B：45.0%
   - 差异：2.0%

==================================================
二、Exponential模型参数对比
==================================================

1. 传播率函数参数：
   - Community A: λ(t) = 0.334274·exp(-0.100000·t)
   - Community B: λ(t) = 0.328571·exp(-0.088009·t)

   参数差异：
   - λ₀差异：0.005703
   - λ₁差异：0.011991

2. 恢复率函数参数：
   - Community A: μ(t) = 0.021563·exp(-0.008605·t)
   - Community B: μ(t) = 0.031248·exp(-0.008317·t)

   参数差异：
   - μ₀差异：0.009685
   - μ₁差异：0.000288

==================================================
三、群体对比分析（基于Community B数据）
==================================================

1. 知识分子群体 vs 普通群众群体：

   感染峰值对比：
   - 知识分子：16.0%（第8轮）
   - 普通群众：36.0%（第27轮）
   - 差异：20.0%

   最终恢复率对比：
   - 知识分子：30.0%
   - 普通群众：30.0%
   - 差异：0.0%

   易感人群减少对比：
   - 知识分子：36.0%
   - 普通群众：54.0%
   - 差异：18.0%

2. 群体特征分析：
   - 普通群众群体更容易感染
   - 两群体恢复能力相近

==================================================
四、Echo室对比总结
==================================================

基于40轮的对比分析，Echo室实验的主要发现：

1. 数据集对比：数据集1传播更强

2. 群体差异：知识分子群体在信息传播中表现出不同的模式

3. 恢复能力：数据集2恢复能力更强

4. 模型特征：两个数据集都使用exponential变系数模型，体现了传播动力学的时变特征

5. Echo室效应：本分析特别关注了不同群体在信息传播中的差异化表现，为理解虚拟社区中的回音室效应提供了数据支持。

==================================================
报告结束
==================================================
